# TODO: Move this values to features.yml itself
# No need to replicate the same values in two places

# ------- Premium Features ------- #
captain:
  name: 'Captain'
  description: 'Enable AI-powered conversations with your customers.'
  enabled: <%= (ChatwootHub.pricing_plan != 'community') %>
  icon: 'icon-captain'
  config_key: 'captain'
  enterprise: true
custom_branding:
  name: 'Custom Branding'
  description: 'Apply your own branding to this installation.'
  enabled: <%= (ChatwootHub.pricing_plan != 'community') %>
  icon: 'icon-paint-brush-line'
  config_key: 'custom_branding'
  enterprise: true
agent_capacity:
  name: 'Agent Capacity'
  description: 'Set limits to auto-assigning conversations to your agents.'
  enabled: <%= (ChatwootHub.pricing_plan != 'community') %>
  icon: 'icon-hourglass-line'
  enterprise: true
audit_logs:
  name: 'Audit Logs'
  description: 'Track and trace account activities with ease with detailed audit logs.'
  enabled: <%= (ChatwootHub.pricing_plan != 'community') %>
  icon: 'icon-menu-search-line'
  enterprise: true
disable_branding:
  name: 'Disable Branding'
  description: 'Disable branding on live-chat widget and external emails.'
  enabled: <%= (ChatwootHub.pricing_plan != 'community') %>
  icon: 'icon-sailbot-fill'
  enterprise: true

# ------- Product Features ------- #
help_center:
  name: 'Help Center'
  description: 'Allow agents to create help center articles and publish them in a portal.'
  enabled: true
  icon: 'icon-book-2-line'

# ------- Communication Channels ------- #
live_chat:
  name: 'Live Chat'
  description: 'Improve your customer experience using a live chat on your website.'
  enabled: true
  icon: 'icon-chat-smile-3-line'
email:
  name: 'Email'
  description: 'Manage your email customer interactions from Chatwoot.'
  enabled: true
  icon: 'icon-mail-send-fill'
  config_key: 'email'
sms:
  name: 'SMS'
  description: 'Manage your SMS customer interactions from Chatwoot.'
  enabled: true
  icon: 'icon-message-line'
messenger:
  name: 'Messenger'
  description: 'Stay connected with your customers on Facebook & Instagram.'
  enabled: true
  icon: 'icon-messenger-line'
  config_key: 'facebook'
instagram:
  name: 'Instagram'
  description: 'Stay connected with your customers on Instagram'
  enabled: true
  icon: 'icon-instagram'
  config_key: 'instagram'
whatsapp:
  name: 'WhatsApp'
  description: 'Manage your WhatsApp business interactions from Chatwoot.'
  enabled: true
  icon: 'icon-whatsapp-line'
telegram:
  name: 'Telegram'
  description: 'Manage your Telegram customer interactions from Chatwoot.'
  enabled: true
  icon: 'icon-telegram-line'
line:
  name: 'Line'
  description: 'Manage your Line customer interactions from Chatwoot.'
  enabled: true
  icon: 'icon-line-line'

# ------- OAuth & Authentication ------- #
google:
  name: 'Google'
  description: 'Configuration for setting up Google OAuth Integration'
  enabled: true
  icon: 'icon-google'
  config_key: 'google'
microsoft:
  name: 'Microsoft'
  description: 'Configuration for setting up Microsoft Email'
  enabled: true
  icon: 'icon-microsoft'
  config_key: 'microsoft'

# ------- Third-party Integrations ------- #
linear:
  name: 'Linear'
  description: 'Configuration for setting up Linear Integration'
  enabled: true
  icon: 'icon-linear'
  config_key: 'linear'
notion:
  name: 'Notion'
  description: 'Configuration for setting up Notion Integration'
  enabled: true
  icon: 'icon-notion'
  config_key: 'notion'
slack:
  name: 'Slack'
  description: 'Configuration for setting up Slack Integration'
  enabled: true
  icon: 'icon-slack'
  config_key: 'slack'
whatsapp_embedded:
  name: 'WhatsApp Embedded'
  description: 'Configuration for setting up WhatsApp Embedded Integration'
  enabled: true
  icon: 'icon-whatsapp-line'
  config_key: 'whatsapp_embedded'
shopify:
  name: 'Shopify'
  description: 'Configuration for setting up Shopify Integration'
  enabled: true
  icon: 'icon-shopify'
  config_key: 'shopify'
