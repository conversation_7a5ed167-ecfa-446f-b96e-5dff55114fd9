{
  "name": "Chatwoot Development Codespace",
  "service": "app",
  "dockerComposeFile": "docker-compose.yml",

  // VS Code 自定义配置
  "customizations": {
    "vscode": {
      "settings": {
        // 终端配置
        "terminal.integrated.defaultProfile.linux": "zsh",

        // 编辑器配置
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
          "source.fixAll.eslint": "explicit",
          "source.fixAll.rubocop": "explicit"
        },
        "editor.rulers": [80, 120],
        "editor.tabSize": 2,
        "editor.insertSpaces": true,

        // 文件配置
        "files.trimTrailingWhitespace": true,
        "files.insertFinalNewline": true,
        "files.associations": {
          "*.html.erb": "erb",
          "*.js.erb": "javascript",
          "*.css.erb": "css"
        },

        // 搜索排除配置
        "search.exclude": {
          "**/node_modules": true,
          "**/tmp": true,
          "**/log": true,
          "**/coverage": true,
          "**/public/packs": true,
          "**/public/assets": true,
          "**/vendor/bundle": true,
          "**/.git": true
        },

        // JavaScript/Vue 配置
        "eslint.validate": ["javascript", "vue"],
        "prettier.requireConfig": true,

        // Git 配置
        "git.autofetch": true,
        "git.enableSmartCommit": true
      },
      "extensions": [
        // Ruby & Rails 开发
        "Shopify.ruby-lsp",
        "misogi.ruby-rubocop",
        "davidpallinder.rails-test-runner",
        "rebornix.ruby",
        "castwide.solargraph",

        // Vue.js & 前端开发
        "Vue.volar",
        "Vue.vscode-typescript-vue-plugin",
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-typescript-next",

        // JavaScript & Node.js
        "esbenp.prettier-vscode",
        "dbaeumer.vscode-eslint",
        "ms-vscode.vscode-json",
        "formulahendry.auto-rename-tag",
        "christian-kohler.path-intellisense",

        // CSS & SCSS
        "stylelint.vscode-stylelint",
        "mrmlnc.vscode-scss",

        // 数据库工具
        "ms-ossdata.vscode-postgresql",
        "ckolkman.vscode-postgres",

        // Git & 版本控制
        "eamodio.gitlens",
        "github.vscode-pull-request-github",
        "donjayamanne.githistory",

        // Docker & 容器
        "ms-azuretools.vscode-docker",

        // 通用开发工具
        "github.copilot",
        "github.copilot-chat",
        "mrmlnc.vscode-duplicate",
        "ms-vscode.vscode-yaml",
        "redhat.vscode-xml",
        "ms-vscode.hexdump",

        // 调试工具
        "ms-vscode.js-debug",
        "KoichiSasada.vscode-rdbg",

        // 测试工具
        "hbenl.vscode-test-explorer",
        "ms-vscode.test-adapter-converter"
      ]
    }
  },


  // 端口映射配置 - 映射所有开发需要的端口
  "forwardPorts": [3000, 3036, 5432, 6379, 8025, 1025],

  // 容器创建后执行的命令 - 分步骤初始化开发环境
  "postCreateCommand": "bash -c 'chmod +x .devcontainer/scripts/setup.sh && .devcontainer/scripts/setup.sh && echo \"📦 安装依赖...\" && pnpm install && echo \"🗄️ 准备数据库...\" && POSTGRES_STATEMENT_TIMEOUT=600s bundle exec rake db:chatwoot_prepare && echo \"🎉 开发环境准备完成！\"'",

  // 容器启动后执行的命令
  "postStartCommand": "echo '🚀 Chatwoot 开发容器已启动！访问 http://localhost:3000 开始开发'",

  // 端口属性配置，提供清晰的标签和说明
  "portsAttributes": {
    "3000": {
      "label": "Rails Server",
      "protocol": "http"
    },
    "3036": {
      "label": "Vite Dev Server",
      "protocol": "http"
    },
    "5432": {
      "label": "PostgreSQL Database"
    },
    "6379": {
      "label": "Redis Cache"
    },
    "8025": {
      "label": "Mailhog Web UI",
      "protocol": "http"
    },
    "1025": {
      "label": "Mailhog SMTP"
    }
  }
}
