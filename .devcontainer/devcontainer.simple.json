{"name": "Chatwoot Development Container (Simple)", "service": "app", "dockerComposeFile": "docker-compose.simple.yml", "workspaceFolder": "/workspace", "customizations": {"vscode": {"settings": {"terminal.integrated.defaultProfile.linux": "bash", "editor.formatOnSave": true, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true}, "extensions": ["Shopify.ruby-lsp", "misogi.ruby-rubocop", "Vue.volar", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "github.copilot"]}}, "forwardPorts": [3000, 3036, 5432, 6379, 8025, 1025], "postCreateCommand": "echo '🚀 简化版开发容器启动完成！'", "portsAttributes": {"3000": {"label": "Rails Server", "protocol": "http"}, "3036": {"label": "Vite Dev Server", "protocol": "http"}, "5432": {"label": "PostgreSQL Database"}, "6379": {"label": "<PERSON><PERSON>"}, "8025": {"label": "Mailhog Web UI", "protocol": "http"}, "1025": {"label": "Mailhog SMTP"}}}