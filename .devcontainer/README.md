# Chatwoot 开发容器配置

这个开发容器配置为 Chatwoot 项目提供了完整的容器化开发环境，支持 Ruby on Rails + Vue.js + Node.js 技术栈。

## 🚀 功能特性

### 技术栈支持
- **Ruby 3.4.4** + **Rails 7.1** - 后端 API 开发
- **Node.js 23.x** + **pnpm 10.x** - 前端构建工具
- **Vue.js 3.x** + **Vite** - 前端框架和开发服务器
- **PostgreSQL 16** with **pgvector** - 数据库（支持向量搜索）
- **Redis 7** - 缓存和会话存储
- **Mailhog** - 邮件测试工具

### VS Code 扩展
自动安装以下开发扩展：

#### Ruby & Rails 开发
- Shopify Ruby LSP - Ruby 语言服务器
- RuboCop - 代码格式化和 linting
- Rails Test Runner - 测试运行器
- Solargraph - Ruby 智能提示

#### 前端开发
- Vue.js 官方扩展 (Volar)
- TypeScript 支持
- Tailwind CSS 智能提示
- Prettier 代码格式化
- ESLint 代码检查

#### 数据库工具
- PostgreSQL 扩展
- 数据库连接和查询工具

#### 通用工具
- GitLens - Git 增强工具
- GitHub Copilot - AI 代码助手
- Docker 支持
- 调试和测试工具

## 🔧 端口映射

| 端口 | 服务 | 说明 |
|------|------|------|
| 3000 | Rails Server | 主应用服务器 |
| 3036 | Vite Dev Server | 前端开发服务器 |
| 5432 | PostgreSQL | 数据库服务 |
| 6379 | Redis | 缓存服务 |
| 8025 | Mailhog Web UI | 邮件测试界面 |
| 1025 | Mailhog SMTP | 邮件发送服务 |

## 🛠️ 使用方法

### 1. 启动开发容器

在 VS Code 中：
1. 安装 "Dev Containers" 扩展
2. 打开项目文件夹
3. 按 `Ctrl+Shift+P` (或 `Cmd+Shift+P`)
4. 选择 "Dev Containers: Reopen in Container"

### 2. 等待初始化

容器启动后会自动执行以下步骤：
1. 🔧 配置开发环境变量
2. ⏳ 等待数据库和 Redis 服务启动
3. 📦 安装项目依赖 (`pnpm install`)
4. 🗄️ 准备数据库 (`rake db:chatwoot_prepare`)

### 3. 启动开发服务器

```bash
# 启动 Rails 服务器
bundle exec rails server -b 0.0.0.0 -p 3000

# 启动 Vite 开发服务器（新终端）
bin/vite dev

# 或者使用 Makefile
make server  # Rails 服务器
```

### 4. 访问应用

- **主应用**: http://localhost:3000
- **Vite 开发服务器**: http://localhost:3036
- **Mailhog 界面**: http://localhost:8025

## 🔍 开发工具配置

### 代码格式化
- 保存时自动格式化
- ESLint 和 RuboCop 自动修复
- Prettier 配置支持

### 调试支持
- Ruby 调试器配置
- JavaScript/Vue.js 调试支持
- 数据库查询工具

### Git 配置
- 自动获取远程更新
- 智能提交功能
- 增强的 Git 历史查看

## 📁 重要文件

- `.devcontainer/devcontainer.json` - 主配置文件
- `.devcontainer/docker-compose.yml` - 服务编排配置
- `.devcontainer/Dockerfile` - 容器镜像配置
- `.devcontainer/scripts/setup.sh` - 初始化脚本

## 🐛 故障排除

### 数据库连接问题
```bash
# 检查 PostgreSQL 状态
pg_isready -h localhost -p 5432 -U postgres

# 重新准备数据库
POSTGRES_STATEMENT_TIMEOUT=600s bundle exec rake db:chatwoot_prepare
```

### Redis 连接问题
```bash
# 检查 Redis 状态
redis-cli -h localhost -p 6379 ping
```

### 依赖安装问题
```bash
# 重新安装依赖
pnpm install
bundle install
```

## 🔧 自定义配置

### 环境变量
编辑 `.env` 文件来自定义配置：
```bash
# 数据库配置
POSTGRES_HOST=localhost
POSTGRES_USERNAME=postgres
POSTGRES_PASSWORD=postgres

# Redis 配置
REDIS_URL=redis://localhost:6379

# 邮件配置
SMTP_ADDRESS=localhost
SMTP_PORT=1025
```

### Git 用户配置
设置环境变量：
```bash
export GIT_USER_NAME="Your Name"
export GIT_USER_EMAIL="<EMAIL>"
```

## 📚 更多资源

- [Chatwoot 官方文档](https://www.chatwoot.com/docs/)
- [VS Code Dev Containers 文档](https://code.visualstudio.com/docs/devcontainers/containers)
- [Docker Compose 文档](https://docs.docker.com/compose/)

---

🎉 **开发愉快！** 如有问题，请查看日志或联系开发团队。
