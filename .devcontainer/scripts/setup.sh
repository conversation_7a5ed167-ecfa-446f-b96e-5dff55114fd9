#!/bin/bash
set -e

echo "🚀 开始设置 Chatwoot 开发环境..."

# 创建环境配置文件
echo "📝 配置环境变量..."
if [ ! -f .env ]; then
  cp .env.example .env
  echo "✅ 已创建 .env 文件"
fi

# 配置开发环境变量
sed -i -e '/REDIS_URL/ s/=.*/=redis:\/\/localhost:6379/' .env
sed -i -e '/POSTGRES_HOST/ s/=.*/=localhost/' .env
sed -i -e '/POSTGRES_USERNAME/ s/=.*/=postgres/' .env
sed -i -e '/POSTGRES_PASSWORD/ s/=.*/=postgres/' .env
sed -i -e '/POSTGRES_DATABASE/ s/=.*/=chatwoot_dev/' .env
sed -i -e '/SMTP_ADDRESS/ s/=.*/=localhost/' .env
sed -i -e '/SMTP_PORT/ s/=.*/=1025/' .env
sed -i -e '/RAILS_ENV/ s/=.*/=development/' .env

# 根据环境设置前端 URL
if [ -n "$CODESPACE_NAME" ]; then
  # GitHub Codespaces 环境
  sed -i -e "/FRONTEND_URL/ s/=.*/=https:\/\/$CODESPACE_NAME-3000.app.github.dev/" .env
  echo "🌐 配置 GitHub Codespaces 环境"
else
  # 本地开发环境
  sed -i -e '/FRONTEND_URL/ s/=.*/=http:\/\/localhost:3000/' .env
  echo "🏠 配置本地开发环境"
fi

# 设置开发工具配置
echo "🔧 配置开发工具..."

# 配置 Git（如果需要）
if [ -n "$GIT_USER_NAME" ] && [ -n "$GIT_USER_EMAIL" ]; then
  git config --global user.name "$GIT_USER_NAME"
  git config --global user.email "$GIT_USER_EMAIL"
  echo "✅ 已配置 Git 用户信息"
fi

# Setup Claude Code API key if available
if [ -n "$CLAUDE_CODE_API_KEY" ]; then
  mkdir -p ~/.claude
  echo '{"apiKeyHelper": "~/.claude/anthropic_key.sh"}' > ~/.claude/settings.json
  echo "echo \"$CLAUDE_CODE_API_KEY\"" > ~/.claude/anthropic_key.sh
  chmod +x ~/.claude/anthropic_key.sh
  echo "✅ 已配置 Claude Code API"
fi

# 等待数据库服务启动
echo "⏳ 等待数据库服务启动..."
timeout=60
while ! pg_isready -h localhost -p 5432 -U postgres > /dev/null 2>&1; do
  if [ $timeout -le 0 ]; then
    echo "❌ 数据库启动超时"
    exit 1
  fi
  echo "等待 PostgreSQL 启动... ($timeout 秒)"
  sleep 2
  timeout=$((timeout - 2))
done
echo "✅ PostgreSQL 已启动"

# 等待 Redis 服务启动
echo "⏳ 等待 Redis 服务启动..."
timeout=30
while ! redis-cli -h localhost -p 6379 ping > /dev/null 2>&1; do
  if [ $timeout -le 0 ]; then
    echo "❌ Redis 启动超时"
    exit 1
  fi
  echo "等待 Redis 启动... ($timeout 秒)"
  sleep 2
  timeout=$((timeout - 2))
done
echo "✅ Redis 已启动"

# GitHub Codespaces 特定配置
if [ -n "$CODESPACE_NAME" ]; then
  echo "🌐 配置 GitHub Codespaces 端口..."
  # 使端口公开可访问
  if command -v gh > /dev/null 2>&1; then
    gh codespace ports visibility 3000:public 3036:public 8025:public -c $CODESPACE_NAME || true
    echo "✅ 已配置端口可见性"
  fi
fi

echo "✅ 开发环境设置完成！"
