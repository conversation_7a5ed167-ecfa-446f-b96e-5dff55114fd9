# 简化版 Docker Compose 配置用于测试
version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
      args:
        VARIANT: 'ubuntu-22.04'
        NODE_VERSION: '23.7.0'
        RUBY_VERSION: '3.4.4'
        USER_UID: '1000'
        USER_GID: '1000'

    volumes:
      - ..:/workspace:cached

    # 基本环境变量配置
    environment:
      - RAILS_ENV=development
      - RAILS_MAX_THREADS=5
      - SECRET_KEY_BASE=development_secret_key_base_for_devcontainer_only
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DATABASE=chatwoot_dev
      - REDIS_URL=redis://redis:6379
      - SMTP_ADDRESS=mailhog
      - SMTP_PORT=1025
      - FRONTEND_URL=http://localhost:3000
      - NODE_ENV=development
      - RAILS_LOG_TO_STDOUT=true

    command: sleep infinity
    
    networks:
      - chatwoot-dev
    
    depends_on:
      - db
      - redis

  db:
    image: pgvector/pgvector:pg16
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_DB: postgres
      POSTGRES_PASSWORD: postgres
    networks:
      - chatwoot-dev

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    networks:
      - chatwoot-dev
    volumes:
      - redis-data:/data

  mailhog:
    image: mailhog/mailhog:latest
    restart: unless-stopped
    networks:
      - chatwoot-dev

volumes:
  postgres-data:
  redis-data:

networks:
  chatwoot-dev:
    driver: bridge
