# https://github.com/microsoft/vscode-dev-containers/blob/master/containers/python-3-postgres/.devcontainer/docker-compose.yml
# https://github.com/microsoft/vscode-dev-containers/blob/master/containers/ruby-rails/.devcontainer/devcontainer.json
#

version: '3'

services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
      args:
        VARIANT: 'ubuntu-22.04'
        NODE_VERSION: '23.7.0'
        RUBY_VERSION: '3.4.4'
        # On Linux, you may need to update USER_UID and USER_GID below if not your local UID is not 1000.
        USER_UID: '1000'
        USER_GID: '1000'

    volumes:
      - ..:/workspace:cached

    # 开发环境变量配置
    environment:
      # Rails 环境配置
      - RAILS_ENV=development
      - RAILS_MAX_THREADS=5
      - SECRET_KEY_BASE=development_secret_key_base_for_devcontainer_only

      # 数据库配置
      - POSTGRES_HOST=localhost
      - POSTGRES_PORT=5432
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DATABASE=chatwoot_dev
      - POSTGRES_STATEMENT_TIMEOUT=600s

      # Redis 配置
      - REDIS_URL=redis://localhost:6379
      - REDIS_PASSWORD=

      # 邮件配置 (Mailhog)
      - SMTP_ADDRESS=localhost
      - SMTP_PORT=1025
      - SMTP_USERNAME=
      - SMTP_PASSWORD=
      - SMTP_AUTHENTICATION=
      - SMTP_ENABLE_STARTTLS_AUTO=false
      - MAILER_SENDER_EMAIL=Chatwoot <<EMAIL>>

      # 前端开发配置
      - FRONTEND_URL=http://localhost:3000
      - VITE_DEV_SERVER_HOST=0.0.0.0
      - NODE_ENV=development

      # 开发工具配置
      - LETTER_OPENER=true
      - RAILS_LOG_TO_STDOUT=true
      - LOG_LEVEL=debug

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Runs app on the same network as the database container, allows "forwardPorts" in devcontainer.json function.
    network_mode: service:db

  db:
    image: pgvector/pgvector:pg16
    restart: unless-stopped
    volumes:
      - postgres-data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: postgres
      POSTGRES_DB: postgres
      POSTGRES_PASSWORD: postgres
      # 优化 PostgreSQL 性能配置
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    network_mode: service:db
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  mailhog:
    restart: unless-stopped
    image: mailhog/mailhog:latest
    network_mode: service:db
    environment:
      MH_STORAGE: maildir
      MH_MAILDIR_PATH: /maildir
    volumes:
      - mailhog-data:/maildir

volumes:
  postgres-data:
  redis-data:
  mailhog-data:
